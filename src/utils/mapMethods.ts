// mapMethods.ts
import Map from 'ol/Map';
import View from 'ol/View';
import { defaults as Defaults } from 'ol/control.js';
import TileLayer from 'ol/layer/Tile';
import XYZ from 'ol/source/XYZ';
import VectorLayer from 'ol/layer/Vector';
import VectorSource from 'ol/source/Vector';
import Feature from 'ol/Feature';
import Point from 'ol/geom/Point';
import { Style, Icon, Stroke, Fill,Text,RegularShape } from 'ol/style';
import Overlay from 'ol/Overlay';
import CircleStyle from 'ol/style/Circle';
import { LineString, Polygon, Geometry } from 'ol/geom';
import { ElMessage } from 'element-plus';
import { mapTileThrottler, networkMonitor } from '@/utils/networkOptimizer';
import { useScreenAdapter } from '@/utils/screenAdapter';

/**
 * 优化的XYZ瓦片源
 * 使用网络优化器进行瓦片请求管理
 */
class OptimizedXYZ extends XYZ {
  constructor(options: any) {
    super(options);

    // 重写瓦片加载函数
    this.setTileLoadFunction((tile: any, src: string) => {
      const img = tile.getImage();

      // 检查网络状态，慢网络下降低瓦片质量
      const networkStatus = networkMonitor.getNetworkStatus();
      if (networkStatus.isSlowNetwork) {
        // 慢网络下可以降低瓦片质量或延迟加载
        setTimeout(() => {
          this.loadTileWithOptimization(img, src);
        }, 100); // 延迟100ms加载
      } else {
        this.loadTileWithOptimization(img, src);
      }
    });
  }

  /**
   * 使用网络优化的瓦片加载 - 重构版
   */
  private loadTileWithOptimization(img: HTMLImageElement, src: string) {
    // 使用重构后的瓦片缓存系统
    mapTileThrottler.throttledTileRequest(src)
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        // 克隆Response以避免body stream被重复读取
        const responseClone = response.clone();
        return responseClone.blob();
      })
      .then(blob => {
        const objectURL = URL.createObjectURL(blob);

        img.onload = () => {
          URL.revokeObjectURL(objectURL);
        };

        img.onerror = () => {
          URL.revokeObjectURL(objectURL);
          console.warn('[OptimizedXYZ] Image load error, falling back to direct URL');
          // 降级到原始加载方式
          img.src = src;
        };

        img.src = objectURL;
      })
      .catch(error => {
        console.warn('[OptimizedXYZ] Tile loading failed:', error);
        // 降级到原始加载方式
        img.src = src;
      });
  }
}

// 存储地图实例
let mapInstance: Map | null = null;
let vectorSource: VectorSource;
// 存储当前打开的弹窗
let currentOverlay: Overlay | null = null;
// 存储所有弹窗
const overlays: Overlay[] = [];

// 性能优化相关变量
let globalEventListenersAdded = false;
let resolutionChangeListeners: Map<string, () => void> = new Map();

// 防抖函数
const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout;
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// 测距相关的变量
let measureMode: boolean = false;
let measurePoints: Feature[] = [];
let measureLine: Feature | null = null;
let distanceLabels: Feature[] = []; // 添加距离标签数组

// 测面积相关的变量
let areaMode: boolean = false;
let areaPoints: Feature[] = [];
let areaPolygon: Feature | null = null;
let areaLabels: Feature[] = [];

// 添加全屏控制变量
let isFullscreen = false;

// 初始化屏幕适配器实例
const screenAdapter = useScreenAdapter({ debug: false });

/**
 * 初始化地图
 * target 地图渲染的dom
 * center 地图中心点
 */
export const initMap = (target: string,center?:number[]): Map => {
  // 创建矢量图层源
  vectorSource = new VectorSource();

  // 天地图图层761d35504d4852532b586f4b529a6889 - 使用优化的XYZ源
  const source = new OptimizedXYZ({
    url: 'http://t4.tianditu.com/DataServer?T=vec_w&tk=9dc4d6ee856d047e1a0caf2eec1c17f8&x={x}&y={y}&l={z}',
    // 添加缓存控制
    cacheSize: 256,
    // 设置最大缩放级别以减少不必要的瓦片请求
    maxZoom: 18
  });
  const tileLayer = new TileLayer({
    source: source,
    className: 'dark-layer',
  });

  // 标注图层 - 使用优化的XYZ源
  const sourceMark = new OptimizedXYZ({
    url: 'http://t4.tianditu.com/DataServer?T=cva_w&tk=9dc4d6ee856d047e1a0caf2eec1c17f8&x={x}&y={y}&l={z}',
    cacheSize: 256,
    maxZoom: 18
  });
  const tileMark = new TileLayer({
    source: sourceMark,
    className: 'dark-layer'
  });

  // 创建矢量图层
  const vectorLayer = new VectorLayer({
    source: vectorSource
  });

  // 创建地图实例
  const map = new Map({
    target: target,
    layers: [tileLayer, tileMark, vectorLayer],
    view: new View({
      projection: 'EPSG:4326',
      center: center || [121.844237,30.063635],
      zoom: 11,
      maxZoom: 20,
      minZoom: 1
    }),
    controls: Defaults({
      zoom: false,
      rotate: false
    })
  });

  // 设置地图实例
  mapInstance = map;

  // 配置地图瓦片缓存
  mapTileThrottler.configure({
    maxCacheSize: 200, // 增加缓存项数量
    maxMemorySize: 150 * 1024 * 1024, // 150MB内存限制
    cacheExpireTime: 24 * 60 * 60 * 1000 // 24小时过期
  });

  // 添加全局事件监听器（只添加一次）
  if (!globalEventListenersAdded) {
    addGlobalEventListeners(map);
    globalEventListenersAdded = true;
  }

  return map;
};

/**
 * 添加全局事件监听器（性能优化）
 */
const addGlobalEventListeners = (map: Map): void => {
  // 全局鼠标移动事件处理（只添加一次）
  map.on('pointermove', (event) => {
    const pixel = map.getEventPixel(event.originalEvent);
    const hit = map.hasFeatureAtPixel(pixel);
    map.getTargetElement().style.cursor = hit ? 'pointer' : '';
  });

  // 全局点击事件处理（只添加一次）
  map.on('click', (event) => {
    map.forEachFeatureAtPixel(event.pixel, (feature) => {
      const pointId = feature.get('id');
      if (pointId) {
        // 查找对应的弹窗
        const targetOverlay = overlays.find(overlay => {
          const element = overlay.getElement();
          return element && element.getAttribute('data-point-id') === pointId;
        });

        if (targetOverlay) {
          const geometry = feature.getGeometry();
          if (geometry instanceof Point) {
            // 如果当前弹窗已经打开，则关闭它
            if (currentOverlay === targetOverlay) {
              targetOverlay.setPosition(undefined);
              currentOverlay = null;
            } else {
              // 关闭其他弹窗
              if (currentOverlay) {
                currentOverlay.setPosition(undefined);
              }
              // 打开当前弹窗
              targetOverlay.setPosition(geometry.getCoordinates());
              currentOverlay = targetOverlay;
            }
          }
        }
      }
    });
  });
};

/**
 * 获取地图实例
 */
export const getMapInstance = (): Map => {
  if (!mapInstance) {
    throw new Error('地图尚未初始化');
  }
  return mapInstance;
};

/**
 * 添加点位
 * coordinates[122.393281, 29.914175] iconPath点位图片 scale 图片缩放 popupContent弹窗,点位id,label点位上面的名字，data船艇数据
 */
export const addPoint = (coordinates: [number, number], iconPath: string,scale:any, popupContent: string,id: string,label?:any,data?:any): void => {
  console.log('addPoint调用:', { coordinates, iconPath, scale, id, label, data });
  const map = getMapInstance();

  const pointFeature = new Feature({
    geometry: new Point(coordinates)
  });
  // 添加唯一标识
  pointFeature.set('id', id);
  // 添加是否显示名称的属性
  pointFeature.set('showLabel', true);

  // 更新点位样式函数（使用防抖优化）
  const updatePointStyle = debounce(() => {
    const zoom = map.getView().getZoom() || 0;
    const showLabel = pointFeature.get('showLabel');
    const isVehicle = data?.isVehicle || false; // 检查是否为车辆

    console.log('updatePointStyle调用:', { zoom, iconPath, isVehicle, id });

    if (zoom < 12 && !isVehicle) {
      // 缩放级别小于12时，非车辆显示绿色散点，车辆仍显示图标
      pointFeature.setStyle(new Style({
        image: new CircleStyle({
          radius: 5,
          fill: new Fill({ color: '#00ff00' }),
          stroke: new Stroke({ color: '#fff', width: 1 })
        }),
        text: undefined
      }));
    } else {
      // 正常显示图标
      if(iconPath){
        console.log('设置图标样式:', { iconPath, scale, id, label });

        // 创建图标样式
        const iconStyle = new Icon({
          src: iconPath,
          scale: scale
        });

        // 监听图标加载事件
        iconStyle.load().then(() => {
          console.log('图标加载成功:', iconPath);
        }).catch((error) => {
          console.error('图标加载失败:', iconPath, error);
        });

        pointFeature.setStyle(new Style({
          image: iconStyle,
          text: (showLabel && label) ? new Text({
            text: label,
            font: 'bold 25px sans-serif',
            fill: new Fill({ color: '#fff' }),
            offsetY: -50,
          }) : undefined
        }));

        console.log('图标样式设置完成:', id);
      }else{
        // 使用绿色三角形替代图片
        const rotation = data?.heading ? (data.heading * Math.PI) / 180 : 0; // 将航向角度转换为弧度
        let color = '#00ff00'; // 默认颜色
        if (data && data.shipType == 1) {
          color = '#b91218'; // 客船
        }else if (data && data.shipType == 2) {
          color = '#eac507'; // 货船
        }else if (data && data.shipType == 3) {
          color = '#29acd2'; // 渔船
        }else if (data && data.shipType == 4) {
          color = '#d84d2e'; // 作业船
        }else if (data && data.shipType == 5) {
          color = '#d569b2'; // 拖船
        }else{
          color = '#00ff00'; // 其他
        }
        pointFeature.setStyle(new Style({
          image: new RegularShape({
            points: 3,
            radius: 10,
            rotation: rotation,
            scale: [1, 2],  // 垂直拉伸形成等腰三角形
            fill: new Fill({ color: color }),
            stroke: new Stroke({ color: '#fff', width: 1 })
          })
        }));
      }

    }
  }, 100); // 100ms 防抖

  // 初始设置样式
  updatePointStyle();

  // 存储监听器以便后续清理
  resolutionChangeListeners.set(id, updatePointStyle);

  // 监听地图缩放事件
  map.getView().on('change:resolution', updatePointStyle);

  vectorSource.addFeature(pointFeature);

  // 只有当popupContent不为空时才创建弹窗
  if (popupContent) {
    // 创建弹窗
    const popupElement = document.createElement('div');
    popupElement.className = 'ol-popup';
    popupElement.innerHTML = `${popupContent}`;
    // 添加点位ID属性
    popupElement.setAttribute('data-point-id', id);

    // 添加关闭按钮样式
    const style = document.createElement('style');
    style.textContent = `
      .popup-close {
        cursor: pointer;
        position: absolute;
        top: 5px;
        right: 5px;
        padding: 4px;
        color: #666;
        text-decoration: none;
        font-weight: bold;
      }
      .popup-close:hover {
        color: #000;
      }
    `;
    document.head.appendChild(style);

    const overlay = new Overlay({
      element: popupElement,
      positioning: 'bottom-center',
      stopEvent: false,
      offset: [0, -50]
    });

    map.addOverlay(overlay);
    overlays.push(overlay);

    // 添加关闭按钮事件
    popupElement.querySelector('.popup-close')?.addEventListener('click', () => {
      overlay.setPosition(undefined);
      currentOverlay = null;
    });
  }
};

/**
 * 设置地图视图中心点
 * @param coordinates 坐标 [经度, 纬度]
 * @param zoomLevel 缩放级别
 */
export const setViewToCoordinates = (
  coordinates: [number, number],
  zoomLevel: number = 16
): void => {
  if(!coordinates || coordinates[0] === null || coordinates[1] === null) return
  const map = getMapInstance();
  map.getView().setCenter(coordinates);
  map.getView().setZoom(zoomLevel);
};

// 添加桥的特征
// 参数: bridgeName - 桥名, coordinates - 经纬度坐标, initialColor - 初始颜色
export const addBridge = (bridgeName: string, coordinates: [number, number][], initialColor: string) => {
  const map = getMapInstance();

  // 确保 vectorSource 存在
  const vectorLayer = map.getLayers().getArray().find(layer => layer instanceof VectorLayer) as VectorLayer;
  if (!vectorLayer) {
    throw new Error('矢量图层未找到');
  }

  const vectorSource = vectorLayer.getSource();
  if (!vectorSource) {
    throw new Error('矢量图层源未找到');
  }

  const bridgeFeature = new Feature({
    geometry: new LineString(coordinates),
    name: bridgeName
  });
  bridgeFeature.setStyle(new Style({
    stroke: new Stroke({
      color: initialColor,
      width: 20
    })
  }));
  vectorSource.addFeature(bridgeFeature);
};

// 根据桥名定位视角并改变颜色
// 参数: bridgeName - 桥名
export const focusOnBridge = (bridgeName: string) => {
  const mapInstance = getMapInstance();
  const vectorSource = mapInstance.getLayers().getArray().find(layer => layer instanceof VectorSource) as VectorSource;

  vectorSource.getFeatures().forEach((feature) => {
    if (feature.get('name') === bridgeName) {
      const geometry = feature.getGeometry();
      if (geometry instanceof Point) {
        const coordinates = geometry.getCoordinates();
        setViewToCoordinates([coordinates[0], coordinates[1]]);
        changeBridgeColor(feature, 'red'); // 假设点击后颜色变为红色
      }
    }
  });
};

// 改变桥的颜色
// 参数: bridgeFeature - 桥要素, color - 颜色
export const changeBridgeColor = (bridgeFeature: Feature, color: string) => {
  bridgeFeature.setStyle(new Style({
    image: new CircleStyle({
      radius: 10,
      fill: new Fill({ color: color }),
      stroke: new Stroke({ color: 'black', width: 2 })
    })
  }));
};

/**
 * 清理事件监听器（性能优化）
 */
export const cleanupEventListeners = (): void => {
  try {
    if (mapInstance) {
      const map = getMapInstance();

      // 清理所有缩放事件监听器
      resolutionChangeListeners.forEach((listener, id) => {
        try {
          map.getView().un('change:resolution', listener);
        } catch (error) {
          // console.warn('清理事件监听器时出错:', error);
        }
      });
      resolutionChangeListeners.clear();

      // 重置全局事件监听器标志
      globalEventListenersAdded = false;
    }
  } catch (error) {
    // console.warn('清理事件监听器时出错:', error);
  }
};

/**
 * 清除所有点位
 */
export const clearAllFeatures = (): void => {
  try {
    // 先清理事件监听器
    cleanupEventListeners();

    if (vectorSource) {
      vectorSource.clear();
    }

    // 清理所有弹窗
    if (mapInstance) {
      const map = getMapInstance();
      overlays.forEach(overlay => {
        try {
          map.removeOverlay(overlay);
        } catch (error) {
          console.warn('移除弹窗时出错:', error);
        }
      });
    }
    overlays.length = 0;
    currentOverlay = null;
  } catch (error) {
    console.warn('清除所有点位时出错:', error);
  }
};

/**
 * 根据id类型清除所有的同类型点位
 */
export const clearTypeFeatures = (type: string): void => {
  const features = vectorSource.getFeatures();
  const map = getMapInstance();

  // 存储要删除的弹窗
  const overlaysToRemove: Overlay[] = [];

  features.forEach(feature => {
    const id = feature.get('id');
    // 添加类型检查和转换
    if (id !== undefined && id !== null) {
      const idString = String(id);
      if (idString.startsWith(type)) {
        // 删除点位
        vectorSource.removeFeature(feature);

        // 清理对应的事件监听器
        const listener = resolutionChangeListeners.get(idString);
        if (listener) {
          try {
            map.getView().un('change:resolution', listener);
            resolutionChangeListeners.delete(idString);
          } catch (error) {
            // console.warn('清理点位事件监听器时出错:', error);
          }
        }

        // 找到对应的弹窗并标记为要删除
        overlays.forEach(overlay => {
          const element = overlay.getElement();
          if (element && element.getAttribute('data-point-id') === idString) {
            overlaysToRemove.push(overlay);
          }
        });
      }
    }
  });

  // 只删除对应类型的弹窗
  overlaysToRemove.forEach(overlay => {
    map.removeOverlay(overlay);
    const index = overlays.indexOf(overlay);
    if (index > -1) {
      overlays.splice(index, 1);
    }
    // 如果当前打开的弹窗是要删除的弹窗，则清空当前弹窗引用
    if (currentOverlay === overlay) {
      currentOverlay = null;
    }
  });
};

/**
 * 获取地图所有点位
 * @returns 返回所有点位的Feature数组
 */
export const getAllPoints = (): Feature[] => {
  const map = getMapInstance();
  const vectorSource = map.getLayers().getArray()
    .find(layer => layer instanceof VectorLayer)?.getSource() as VectorSource;

  if (!vectorSource) {
    throw new Error('未找到矢量图层源');
  }

  return vectorSource.getFeatures();
};

/**
 * 根据Feature删除点位
 * @param feature 点位Feature对象
 */
export const deletePointByFeature = (feature: Feature): void => {
  vectorSource.removeFeature(feature);
};



/**
 * 根据ID获取点位
 * @param id 点位标识
 * @returns 返回对应的点位Feature
 */
export const getPointById = (id: string): Feature | undefined => {
  const points = getAllPoints();
  return points.find(point => point.get('id') === id);
};

/**
 * 根据点位Feature更新经纬度
 * @param pointFeature 点位Feature对象
 * @param newCoordinates 新的经纬度坐标 [经度, 纬度]
 */
export const updatePointCoordinates = (pointFeature: Feature, newCoordinates: [number, number]): void => {
  const geometry = pointFeature.getGeometry();
  if (geometry instanceof Point) {
    geometry.setCoordinates(newCoordinates);
  } else {
    throw new Error('该要素的几何类型不是点');
  }
};

/**
 * 更新点位名称
 * @param pointFeature 点位Feature对象
 * @param newName 新的名称
 */
export const updatePointName = (pointFeature: Feature, newName: string): void => {
  // 设置是否显示名称的属性
  pointFeature.set('showLabel', !!newName);

  const style = pointFeature.getStyle();
  if (style instanceof Style) {
    const newStyle = new Style({
      image: style.getImage(),
      text: newName ? new Text({
        text: newName,
        font: 'bold 25px sans-serif',
        fill: new Fill({ color: '#fff' }),
        offsetY: -50,
      }) : undefined
    });
    pointFeature.setStyle(newStyle);
  }
};

/**
 * 处理键盘事件
 */
const handleKeyPress = (event: KeyboardEvent): void => {
  if (event.code === 'Space' && measureMode) {
    endMeasure();
  }
};

/**
 * 开始测距模式
 */
export const startMeasure = (): void => {
  ElMessage.success('开始测距')
  const map = getMapInstance();
  measureMode = true;
  measurePoints = [];
  measureLine = null;

  // 添加点击事件监听
  map.on('click', handleMeasureClick);

  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeyPress);
};

/**
 * 显示距离标签
 */
const showDistanceLabel = (from: number[], to: number[], distance: number): void => {
  const midPoint = [(from[0] + to[0]) / 2, (from[1] + to[1]) / 2];

  const labelFeature = new Feature({
    geometry: new Point(midPoint)
  });

  labelFeature.setStyle(new Style({
    text: new Text({
      text: `${(distance / 1000).toFixed(2)} km`,
      font: '25px Arial blod',
      fill: new Fill({ color: '#000' }),
      stroke: new Stroke({ color: '#fff', width: 30 }),
      offsetY: -10
    })
  }));

  vectorSource.addFeature(labelFeature);
  distanceLabels.push(labelFeature); // 将标签添加到数组中
};

/**
 * 结束测距模式
 */
export const endMeasure = (): void => {
  ElMessage.success('结束测距')
  const map = getMapInstance();
  measureMode = false;

  // 移除点击事件监听
  map.un('click', handleMeasureClick);

  // 移除键盘事件监听
  document.removeEventListener('keydown', handleKeyPress);

  // 清除测量点和线
  if (measureLine) {
    vectorSource.removeFeature(measureLine);
  }
  measurePoints.forEach(point => vectorSource.removeFeature(point));

  // 清除距离标签
  distanceLabels.forEach(label => vectorSource.removeFeature(label));

  // 重置所有数组
  measurePoints = [];
  measureLine = null;
  distanceLabels = []; // 清空距离标签数组
};

/**
 * 处理测距点击事件
 */
const handleMeasureClick = (event: any): void => {
  if (!measureMode) return;

  const map = getMapInstance();
  const coordinate = map.getEventCoordinate(event.originalEvent);

  // 创建点击点
  const pointFeature = new Feature({
    geometry: new Point(coordinate)
  });
  pointFeature.setStyle(new Style({
    image: new CircleStyle({
      radius: 5,
      fill: new Fill({ color: '#ff0000' }),
      stroke: new Stroke({ color: '#fff', width: 10 })
    })
  }));

  vectorSource.addFeature(pointFeature);
  measurePoints.push(pointFeature);

  // 如果有两个或更多点，绘制连线
  if (measurePoints.length >= 2) {
    const coordinates = measurePoints.map(point =>
      (point.getGeometry() as Point).getCoordinates()
    );

    if (measureLine) {
      vectorSource.removeFeature(measureLine);
    }

    measureLine = new Feature({
      geometry: new LineString(coordinates)
    });

    measureLine.setStyle(new Style({
      stroke: new Stroke({
        color: '#71bcf3',
        width: 8
      })
    }));

    vectorSource.addFeature(measureLine);

    // 计算并显示距离
    const distance = calculateDistance(coordinates);
    showDistanceLabel(coordinates[coordinates.length - 2], coordinates[coordinates.length - 1], distance);
  }
};

/**
 * 计算两点之间的距离（米）
 */
const calculateDistance = (coordinates: number[][]): number => {
  let totalDistance = 0;
  for (let i = 1; i < coordinates.length; i++) {
    const from = coordinates[i - 1];
    const to = coordinates[i];

    // 使用 Haversine 公式计算球面距离
    const R = 6371000; // 地球半径（米）
    const φ1 = from[1] * Math.PI / 180;
    const φ2 = to[1] * Math.PI / 180;
    const Δφ = (to[1] - from[1]) * Math.PI / 180;
    const Δλ = (to[0] - from[0]) * Math.PI / 180;

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const distance = R * c;

    totalDistance += distance;
  }
  return totalDistance;
};

/**
 * 开始测面积模式
 */
export const startAreaMeasure = (): void => {
  ElMessage.success('开始测面积');
  const map = getMapInstance();
  areaMode = true;
  areaPoints = [];
  areaPolygon = null;
  areaLabels = [];

  // 添加点击事件监听
  map.on('click', handleAreaClick);

  // 添加键盘事件监听
  document.addEventListener('keydown', handleAreaKeyPress);
};

/**
 * 处理测面积点击事件
 */
const handleAreaClick = (event: any): void => {
  if (!areaMode) return;

  const map = getMapInstance();
  const coordinate = map.getEventCoordinate(event.originalEvent);

  // 创建点击点
  const pointFeature = new Feature({
    geometry: new Point(coordinate)
  });
  pointFeature.setStyle(new Style({
    image: new CircleStyle({
      radius: 5,
      fill: new Fill({ color: '#ff0000' }),
      stroke: new Stroke({ color: '#fff', width: 10 })
    })
  }));

  vectorSource.addFeature(pointFeature);
  areaPoints.push(pointFeature);

  // 如果有三个或更多点，绘制多边形
  if (areaPoints.length >= 3) {
    const coordinates = areaPoints.map(point =>
      (point.getGeometry() as Point).getCoordinates()
    );
    // 闭合多边形
    coordinates.push(coordinates[0]);

    if (areaPolygon) {
      vectorSource.removeFeature(areaPolygon);
    }

    areaPolygon = new Feature({
      geometry: new Polygon([coordinates])
    });

    areaPolygon.setStyle(new Style({
      fill: new Fill({
        color: 'rgba(113, 188, 243, 0.3)'
      }),
      stroke: new Stroke({
        color: '#71bcf3',
        width: 8
      })
    }));

    vectorSource.addFeature(areaPolygon);

    // 清除之前的面积标签
    areaLabels.forEach(label => vectorSource.removeFeature(label));
    areaLabels = [];

    // 计算并显示总面积
    const area = calculateArea(coordinates);
    showAreaLabel(coordinates, area);
  }
};

/**
 * 处理测面积键盘事件
 */
const handleAreaKeyPress = (event: KeyboardEvent): void => {
  if (event.code === 'Space' && areaMode) {
    endAreaMeasure();
  }
};

/**
 * 结束测面积模式
 */
export const endAreaMeasure = (): void => {
  ElMessage.success('结束测面积');
  const map = getMapInstance();
  areaMode = false;

  // 移除点击事件监听
  map.un('click', handleAreaClick);

  // 移除键盘事件监听
  document.removeEventListener('keydown', handleAreaKeyPress);

  // 清除测量点和多边形
  if (areaPolygon) {
    vectorSource.removeFeature(areaPolygon);
  }
  areaPoints.forEach(point => vectorSource.removeFeature(point));

  // 清除面积标签
  areaLabels.forEach(label => vectorSource.removeFeature(label));

  // 重置所有数组
  areaPoints = [];
  areaPolygon = null;
  areaLabels = [];
};

/**
 * 计算多边形面积（平方米）
 */
const calculateArea = (coordinates: number[][]): number => {
  const R = 6371000; // 地球半径（米）
  let area = 0;
  const n = coordinates.length - 1; // 减去闭合点

  for (let i = 0; i < n; i++) {
    const j = (i + 1) % n;
    // 将经纬度转换为弧度
    const lat1 = coordinates[i][1] * Math.PI / 180;
    const lat2 = coordinates[j][1] * Math.PI / 180;
    const lon1 = coordinates[i][0] * Math.PI / 180;
    const lon2 = coordinates[j][0] * Math.PI / 180;

    // 使用改进的球面多边形面积公式
    area += (lon2 - lon1) * (2 + Math.sin(lat1) + Math.sin(lat2));
  }

  // 计算最终面积
  area = Math.abs(area * R * R / 2);

  // 如果面积太小，返回0
  if (area < 0.01) {
    return 0;
  }

  return area;
};

/**
 * 显示面积标签
 */
const showAreaLabel = (coordinates: number[][], area: number): void => {
  // 计算多边形的中心点
  let centerX = 0;
  let centerY = 0;
  const n = coordinates.length - 1; // 减去闭合点

  for (let i = 0; i < n; i++) {
    centerX += coordinates[i][0];
    centerY += coordinates[i][1];
  }
  centerX /= n;
  centerY /= n;

  const labelFeature = new Feature({
    geometry: new Point([centerX, centerY])
  });

  // 格式化面积显示
  let areaText = '';
  if (area >= 1000000) {
    areaText = `总面积: ${(area / 1000000).toFixed(2)} km²`;
  } else {
    areaText = `总面积: ${area.toFixed(2)} m²`;
  }

  labelFeature.setStyle(new Style({
    text: new Text({
      text: areaText,
      font: '25px Arial blod',
      fill: new Fill({ color: '#000' }),
      stroke: new Stroke({ color: '#fff', width: 30 }),
      offsetY: 0
    })
  }));

  vectorSource.addFeature(labelFeature);
  areaLabels.push(labelFeature);
};

/**
 * 切换页面全屏状态
 */
export const toggleFullscreen = (): void => {
  const map = getMapInstance();

  if (!isFullscreen) {
    // 进入页面全屏
    console.log('[Fullscreen] 进入页面全屏模式');
    if (document.documentElement.requestFullscreen) {
      document.documentElement.requestFullscreen();
    } else if ((document.documentElement as any).webkitRequestFullscreen) {
      (document.documentElement as any).webkitRequestFullscreen();
    } else if ((document.documentElement as any).msRequestFullscreen) {
      (document.documentElement as any).msRequestFullscreen();
    }
  } else {
    // 退出全屏
    console.log('[Fullscreen] 退出页面全屏模式');
    if (document.exitFullscreen) {
      document.exitFullscreen();
    } else if ((document as any).webkitExitFullscreen) {
      (document as any).webkitExitFullscreen();
    } else if ((document as any).msExitFullscreen) {
      (document as any).msExitFullscreen();
    }
  }

  isFullscreen = !isFullscreen;

  // 更新屏幕适配器的全屏状态
  screenAdapter.updateFullscreenState();

  // 触发地图重新渲染
  setTimeout(() => {
    map.updateSize();
  }, 100); // 延迟一点确保全屏状态已经改变
};

/**
 * 监听全屏变化事件
 */
export const initFullscreenListener = (): void => {
  document.addEventListener('fullscreenchange', handleFullscreenChange);
  document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
  document.addEventListener('mozfullscreenchange', handleFullscreenChange);
  document.addEventListener('MSFullscreenChange', handleFullscreenChange);
};

/**
 * 处理全屏变化事件
 */
export const handleFullscreenChange = (): void => {
  const map = getMapInstance();
  isFullscreen = !!(
    document.fullscreenElement ||
    (document as any).webkitFullscreenElement ||
    (document as any).mozFullScreenElement ||
    (document as any).msFullscreenElement
  );

  // 更新地图大小
  map.updateSize();
};

/**
 * 创建电子围栏
 * @param data 围栏数据对象
 */
export const createElectronicFence = (data: {
  id: number;
  name: string;
  borderColor: string;
  borderWidth: number;
  fillColor: string;
  regions: string;
}): void => {
  const map = getMapInstance();

  try {
    // 解析区域坐标
    const regionsData = JSON.parse(data.regions);
    const coordinates = regionsData[0].map((point: { lng: number; lat: number }) =>
      [point.lng, point.lat]
    );

    // 创建多边形要素
    const polygonFeature = new Feature({
      geometry: new Polygon([coordinates])
    });

    // 设置样式
    polygonFeature.setStyle(new Style({
      fill: new Fill({
        color: data.fillColor
      }),
      stroke: new Stroke({
        color: data.borderColor,
        width: data.borderWidth
      })
    }));

    // 添加属性
    polygonFeature.setProperties({
      id: data.id,
      name: data.name,
      type: 'electronic_fence'
    });

    // 添加到地图
    vectorSource.addFeature(polygonFeature);

    // 添加标签
    const center = calculatePolygonCenter(coordinates);
    const labelFeature = new Feature({
      geometry: new Point(center)
    });

    labelFeature.setStyle(new Style({
      text: new Text({
        text: data.name,
        font: 'bold 14px Arial',
        fill: new Fill({ color: '#000' }),
        stroke: new Stroke({ color: '#fff', width: 3 }),
        offsetY: 0
      })
    }));

    vectorSource.addFeature(labelFeature);

  } catch (error) {
    console.error('创建电子围栏失败:', error);
    ElMessage.error('创建电子围栏失败');
  }
};

/**
 * 计算多边形中心点
 * @param coordinates 坐标数组
 * @returns 中心点坐标
 */
const calculatePolygonCenter = (coordinates: number[][]): number[] => {
  let centerX = 0;
  let centerY = 0;
  const n = coordinates.length;

  for (let i = 0; i < n; i++) {
    centerX += coordinates[i][0];
    centerY += coordinates[i][1];
  }

  return [centerX / n, centerY / n];
};

/**
 * 获取地图缓存统计信息
 */
export const getMapCacheStats = () => {
  return mapTileThrottler.getCacheStats();
};

/**
 * 清理地图缓存
 */
export const clearMapCache = () => {
  mapTileThrottler.clearCache();
};

/**
 * 配置地图缓存
 */
export const configureMapCache = (options: {
  maxCacheSize?: number;
  maxMemorySize?: number;
  cacheExpireTime?: number;
}) => {
  mapTileThrottler.configure(options);
};

// 其他地图方法可以在这里定义
