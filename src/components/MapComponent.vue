<template>
  <div id="base-map" class="map"></div>

  <!-- 执法船监控弹窗 -->
  <PopUp :visible="showMonitorModal" title="执法船监控" :width="'100%'" @update:visible="showMonitorModal = $event" class="PopUp1">
    <div class="modal-content">
      <VideoList :monitor-list="monitorList" :mmsi="selectedMmsi" />
    </div>
  </PopUp>
</template>

<script setup lang="ts">
import { h, ref, onMounted, onBeforeUnmount, nextTick } from 'vue';
import { useRoute } from 'vue-router';
import {
  getMapInstance,
  initMap,
  addPoint,
  getPointById,
  updatePointCoordinates,
  initFullscreenListener,
  handleFullscreenChange,
  createElectronicFence,
  cleanupEventListeners
} from '@/utils/mapMethods';
import { getShipRealtime, getNavigationEnv, getUrlVideo, getBridgeRange, getVehicleGpsTracking, getHazardousVehicle } from '@/api/bridge/point';
import shipImg from '@/assets/svg/ship.svg';
import chuan2Img from '@/assets/home/<USER>';
import sxtImg from '@/assets/forewarning/sxt.png';
import carImg from '@/assets/svg/car_danger.png';
import cheImg from '@/assets/home/<USER>';
import { createApp } from 'vue';
import PopUp from '@/components/PopUp/index.vue';
import VideoList from '@/components/VideoList/index.vue';

// 定时器
let timer: any | null = null;
let retryCount = 0;
const MAX_RETRIES = 5;
const TIMEOUT_DURATION = 600000; // 30秒

// 路由
const route = useRoute();

// 防抖函数
const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout;
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// 监控弹窗相关
const showMonitorModal = ref(false);
const monitorList = ref([]);
const selectedMmsi = ref<string | null>(null);

//获取执法船点位数据
const getShipRealtimeFc = async () => {
  const res: any = await getShipRealtime({});
  console.log('船舶数据响应:', res);
  if (res.code === 200) {
    res.data.forEach((item: any) => {
      let pointImg = '';
      let salace = 0;
      const pointId = `point_${item.mmsi}`;
      if (
        item.mmsi == '413435150' ||
        item.mmsi == '413234960' ||
        item.mmsi == '413304390' ||
        item.mmsi == '413288130' ||
        item.mmsi == '414403740' ||
        item.mmsi == '413366040'
      ) {
        pointImg = shipImg;
        salace = 0.1;
      } else {
        pointImg = '';
        salace = 1;
      }
      //判断有没有
      const existingPoint = getPointById(pointId);

      if (existingPoint) {
        // 更新点位坐标
        updatePointCoordinates(existingPoint, [item.lon, item.lat]);
      } else {
        // 弹窗内容
        const popupContent = `
          <div class="popup-title" style="min-width:460px;min-height:60px;display:flex;justify-content: space-between;align-items: center;background: linear-gradient(141deg, rgba(40, 40, 61, 0.85) 0%, rgba(0, 0, 0, 0.57) 44%, rgba(4, 3, 9, 0) 100%);padding:0px 25px;">
            <div style="display: flex; align-items: center; gap: 10px;">
              <span style="color:#fff;font-size:25px;font-weight: bold;">${item.name}</span>
              ${
                item.mmsi == '413435150' ||
                item.mmsi == '413234960' ||
                item.mmsi == '413304390' ||
                item.mmsi == '413288130' ||
                item.mmsi == '414403740' ||
                item.mmsi == '413366040'
                  ? '<img src="' +
                    sxtImg +
                    '" style="width: 30px; height: 30px; cursor: pointer;" title="查看监控" class="monitor-icon" data-mmsi="' +
                    item.mmsi +
                    '" />'
                  : ''
              }
            </div>
            <button class="popup-close" style="font-size:20px">X</button>
          </div>
          <div class="popup-content" style="width:auto;min-height:200px;background: linear-gradient(141deg, rgba(40, 40, 61, 0.85) 0%, rgba(0, 0, 0, 0.57) 44%, rgba(4, 3, 9, 0) 100%);padding:25px;border-top: 1px solid #accbff; ">
            <div class="ship-popup" style="min-width:700px;width:auto;height:auto;">
              <div class="ship-info" style="font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">MMSI: ${item.mmsi}</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">呼号:${item.callsign} </span>
              </div>
              <div class="ship-info" style="font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">IMO: </span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">经度: ${item.lon},${item.lat}</span>
              </div>
              <div class="ship-info" style="font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">类型:${handleShipType(item.shipType)} </span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">航速:${item.sog} </span>
              </div>
              <div class="ship-info" style="font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">船长:${item.toBow + item.toStern} </span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">目的地:${item.destination} </span>
              </div>
               <div class="ship-info" style="font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">船宽:${item.toPort + item.toStarboard} </span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">预到时间: </span>
              </div>
              <div class="ship-info" style="font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">吃水: </span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">更新时间:${item.updateTime} </span>
              </div>
              <div class="ship-info" style="font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">航向:${item.cog} </span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;"></span>
              </div>
            </div>
          </div>
        `;
        // .toFixed(6)
        addPoint([item.lon, item.lat], pointImg, salace, popupContent, pointId, '', item);
      }
    });
  }
};

//获取车辆定位数据
const getVehicleLocationFc = async () => {
  // 只在特定路由显示车辆定位
  if (route.path !== '/secialVehicle') {
    return;
  }

  try {
    // 获取车辆GPS定位信息
    const gpsRes: any = await getVehicleGpsTracking({});
    // 获取车辆详细信息
    const vehicleRes: any = await getHazardousVehicle({});

    if (gpsRes.code === 200 && vehicleRes.code === 200) {

      // 创建车辆信息映射表，方便快速查找
      const vehicleInfoMap = new Map();
      if (vehicleRes.rows && vehicleRes.rows.length > 0) {
        vehicleRes.rows.forEach((vehicle: any) => {
          vehicleInfoMap.set(vehicle.id, vehicle);
        });
      }

      // 处理GPS定位数据
      if (gpsRes.rows && gpsRes.rows.length > 0) {
        gpsRes.rows.forEach((item: any) => {

        // 验证坐标数据
        if (!item.lon || !item.lat || isNaN(parseFloat(item.lon)) || isNaN(parseFloat(item.lat))) {
          console.warn('无效的坐标数据，跳过该车辆:', item);
          return;
        }

        const pointId = `vehicle_${item.vehicleId}`;
        const vehicleInfo = vehicleInfoMap.get(item.vehicleId);
        console.log('车辆信息匹配结果:', vehicleInfo);

        // 设置车辆图标和缩放比例
        const pointImg = carImg; // 使用PNG格式的车辆图标
        const scale = 0.2; // 适当的缩放比例

        console.log('使用的图标路径:', pointImg);
        console.log('图标路径类型:', typeof pointImg);
        console.log('船舶图标路径对比:', shipImg);

        // 检查是否已存在该点位
        const existingPoint = getPointById(pointId);

        if (existingPoint) {
          // 更新点位坐标
          console.log('更新车辆点位坐标:', pointId, [parseFloat(item.lon), parseFloat(item.lat)]);
          updatePointCoordinates(existingPoint, [parseFloat(item.lon), parseFloat(item.lat)]);
        } else {
          // 创建弹窗内容
          const popupContent = `
            <div class="popup-title" style="min-width:460px;min-height:60px;display:flex;justify-content: space-between;align-items: center;background: linear-gradient(141deg, rgba(40, 40, 61, 0.85) 0%, rgba(0, 0, 0, 0.57) 44%, rgba(4, 3, 9, 0) 100%);padding:0px 25px;">
              <div style="display: flex; align-items: center; gap: 10px;">
                <span style="color:#fff;font-size:25px;font-weight: bold;">${vehicleInfo ? vehicleInfo.licensePlate : '未知车辆'}</span>
                <img src="${cheImg}" style="width:30px;height:30px;" />
              </div>
            </div>
            <div style="background: rgba(0, 0, 0, 0.5);padding:20px 25px;">
              <div class="vehicle-info" style="font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">车牌号: ${vehicleInfo ? vehicleInfo.licensePlate : '未知'}</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">车辆类型: ${vehicleInfo ? vehicleInfo.vehicleType : '未知'}</span>
              </div>
              <div class="vehicle-info" style="font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">所属公司: ${vehicleInfo ? vehicleInfo.owner : '未知'}</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">驾驶员: ${vehicleInfo ? vehicleInfo.driverName : '未知'}</span>
              </div>
              <div class="vehicle-info" style="font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">速度: ${item.speed || 0} km/h</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">方向: ${item.direction || 0}°</span>
              </div>
              <div class="vehicle-info" style="font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">状态: ${item.status || '未知'}</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">GPS时间: ${item.gpsTimeStr || '未知'}</span>
              </div>
            </div>
          `;

          // 添加点位 - 使用PNG格式的车辆图标
          const coordinates = [parseFloat(item.lon), parseFloat(item.lat)];
          console.log('添加车辆点位:', pointId, coordinates);
          console.log('弹窗内容长度:', popupContent.length);
          console.log('使用车辆图标:', pointImg);

          addPoint(coordinates, pointImg, scale, popupContent, pointId, vehicleInfo ? vehicleInfo.licensePlate : `车辆${item.vehicleId}`, {
            ...item,
            vehicleInfo,
            vehicleType: vehicleInfo ? vehicleInfo.vehicleType : '未知',
            isVehicle: true // 标记为车辆类型
          });

          console.log('车辆点位添加完成:', pointId);
        }
        });
      }
      console.log('车辆定位数据处理完成');
    } else {
      console.log('接口返回失败:', { gpsRes, vehicleRes });
    }
  } catch (error) {
    console.error('获取车辆定位数据失败:', error);
  }
};

//测试车辆图标显示
const testVehicleIcon = () => {
  console.log('测试车辆图标显示...');
  const testCoordinates = [122.393281, 29.914175]; // 使用一个固定的测试坐标
  const testPopupContent = `
    <div class="popup-title" style="min-width:460px;min-height:60px;display:flex;justify-content: space-between;align-items: center;background: linear-gradient(141deg, rgba(40, 40, 61, 0.85) 0%, rgba(0, 0, 0, 0.57) 44%, rgba(4, 3, 9, 0) 100%);padding:0px 25px;">
      <div style="display: flex; align-items: center; gap: 10px;">
        <span style="color:#fff;font-size:25px;font-weight: bold;">测试车辆</span>
        <img src="${cheImg}" style="width:30px;height:30px;" />
      </div>
    </div>
    <div style="background: rgba(0, 0, 0, 0.5);padding:20px 25px;">
      <div class="vehicle-info" style="font-size: 20px;color: #fff;">
        <span>这是一个测试车辆图标</span>
      </div>
    </div>
  `;

  console.log('添加测试车辆点位，图标路径:', carImg);
  addPoint(testCoordinates, carImg, 0.3, testPopupContent, 'test_vehicle', '测试车辆', { isVehicle: true });
  console.log('测试车辆点位添加完成');
};

//获取电子围栏数据
const getNavigationEnvFc = async () => {
  const res = await getNavigationEnv();
  const res2 = await getBridgeRange(); //五个桥的在舶范围
  if (res.code == 200) {
    res.data.forEach((item: any) => {
      createElectronicFence({
        id: item.id,
        name: item.name,
        borderColor: item.borderColor,
        borderWidth: item.borderWidth,
        fillColor: item.fillColor,
        regions: item.regions
      });
    });
  }
  if (res2.code == 200) {
    res2.data.forEach((item) => {
      createElectronicFence({
        id: item.id,
        name: item.name,
        borderColor: item.borderColor,
        borderWidth: item.borderWidth,
        fillColor: item.fillColor,
        regions: item.regions
      });
    });
  }
};
//处理船类型
const handleShipType = (type: string) => {
  if (type == '1') {
    return '客船';
  } else if (type == '2') {
    return '货船';
  } else if (type == '3') {
    return '渔船';
  } else if (type == '4') {
    return '作业船';
  } else if (type == '5') {
    return '拖船';
  } else {
    return '其他';
  }
};

//调用五次接口还是失败就不用调用了
const getRealtimeDataWithRetry = async () => {
  try {
    // 同时获取船舶和车辆数据
    await Promise.all([
      getShipRealtimeFc(),
      getVehicleLocationFc()
    ]);
    // 如果成功，重置重试计数
    retryCount = 0;
    // 设置下一次调用
    timer = setTimeout(getRealtimeDataWithRetry, TIMEOUT_DURATION);
  } catch (error) {
    retryCount++;
    console.error(`获取数据失败，第 ${retryCount} 次重试`);

    if (retryCount >= MAX_RETRIES) {
      // 达到最大重试次数，清除定时器
      if (timer) {
        clearTimeout(timer);
        timer = null;
      }
      console.error(timer, '达到最大重试次数，停止获取数据');
    } else {
      // 如果还没达到最大重试次数，继续尝试
      timer = setTimeout(getRealtimeDataWithRetry, TIMEOUT_DURATION);
    }
  }
};

// 获取监控列表数据
const getMonitorList = async () => {
  const res: any = await getUrlVideo();
  if (res) {
    monitorList.value = res || [];
  }
};

// 打开监控弹窗
const openMonitorModal = async () => {
  showMonitorModal.value = true;
  // 确保数据已加载
  if (monitorList.value.length === 0) {
    await getMonitorList();
  }
  // 数据加载完成后再设置mmsi，触发监听
  nextTick(() => {
    selectedMmsi.value = selectedMmsi.value;
  });
};

// 使用 onMounted 生命周期钩子
onMounted(() => {
  initMap('base-map');
  initFullscreenListener();
  getShipRealtimeFc();
  getVehicleLocationFc(); // 添加车辆定位数据获取
  testVehicleIcon(); // 添加测试车辆图标
  getNavigationEnvFc();
  timer = setTimeout(getRealtimeDataWithRetry, TIMEOUT_DURATION);

  // 添加事件委托监听
  document.addEventListener('click', handleMonitorIconClick);
});

onBeforeUnmount(() => {
  try {
    if (timer) {
      clearTimeout(timer); // 清除定时器
      timer = null;
    }

    // 清理地图事件监听器
    cleanupEventListeners();

    // 移除全屏事件监听
    document.removeEventListener('fullscreenchange', handleFullscreenChange);
    document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
    document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    // 移除监控图标点击事件监听
    document.removeEventListener('click', handleMonitorIconClick);
  } catch (error) {
    console.warn('组件销毁时清理资源出错:', error);
  }
});

// 处理监控图标点击事件
const handleMonitorIconClick = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  if (target.classList.contains('monitor-icon')) {
    const mmsi = target.getAttribute('data-mmsi');
    if (mmsi) {
      // 先设置mmsi，再打开弹窗
      selectedMmsi.value = mmsi;
      openMonitorModal();
    }
  }
};
</script>

<style scoped lang="scss">
.map {
  width: 100%;
  height: 100%;
  :deep(.dark-layer) {
    filter: contrast(102%) brightness(93%) saturate(103%) sepia(65%) grayscale(22%) invert(100%);
  }

  :deep(.monitor-icon) {
    transition: transform 0.3s;
    &:hover {
      transform: scale(1.2);
    }
  }
}
.PopUp1 {
  width: 100%;
  height: 100%;
  .modal-content {
    width: 100%;
    height: 100%;
  }
}
</style>
